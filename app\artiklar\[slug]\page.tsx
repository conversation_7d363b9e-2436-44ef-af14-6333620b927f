import type { <PERSON><PERSON><PERSON> } from "next"
import Image from "next/image"
import { getAllPosts, getPostBySlug } from "@/lib/blog"
import { MDXRemote } from "next-mdx-remote/rsc"
import rehypeHighlight from "rehype-highlight"
import ArticleClient from "./ArticleClient"
import { ArticleSchema, BreadcrumbSchema } from "@/components/structured-data"

// MDX components for server-side rendering
const components = {
  h1: (props: any) => <h1 className="font-crimson text-3xl font-bold text-white mt-8 mb-4" {...props} />,
  h2: (props: any) => <h2 className="font-crimson text-2xl font-bold text-white mt-6 mb-3" {...props} />,
  h3: (props: any) => <h3 className="font-crimson text-xl font-bold text-amber-400 mt-4 mb-2" {...props} />,
  p: (props: any) => <p className="text-slate-300 leading-relaxed mb-4" {...props} />,
  ul: (props: any) => <ul className="list-disc list-inside space-y-2 mb-4 text-slate-300" {...props} />,
  ol: (props: any) => <ol className="list-decimal list-inside space-y-2 mb-4 text-slate-300" {...props} />,
  li: (props: any) => <li className="text-slate-300" {...props} />,
  a: (props: any) => <a className="text-amber-400 hover:text-amber-300 underline" {...props} />,
  blockquote: (props: any) => (
    <blockquote className="border-l-4 border-amber-500 pl-4 italic text-slate-400 my-4" {...props} />
  ),
  code: (props: any) => <code className="bg-slate-800 px-1 py-0.5 rounded text-amber-300" {...props} />,
  pre: (props: any) => <pre className="bg-slate-800 p-4 rounded-lg overflow-x-auto my-4" {...props} />,
  img: (props: any) => (
    <Image {...props} alt={props.alt || ""} width={800} height={400} className="rounded-lg w-full h-auto my-6" />
  ),
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  const posts = await getAllPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

// Generate metadata for each blog post
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  const post = await getPostBySlug(slug)

  if (!post) {
    return {
      title: "Artikel inte hittad",
      description: "Den begärda artikeln kunde inte hittas.",
    }
  }

  return {
    title: post.title,
    description: post.excerpt,
    keywords: post.keywords,
    alternates: {
      canonical: `https://pappahaj.se/artiklar/${slug}`,
    },
    openGraph: {
      title: `${post.title} | Pappa Haj`,
      description: post.excerpt,
      url: `https://pappahaj.se/artiklar/${slug}`,
      type: "article",
      publishedTime: post.date,
      images: [
        {
          url: post.image,
          width: 800,
          height: 400,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [post.image],
    },
  }
}

export default async function ArticlePage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const post = await getPostBySlug(slug)

  if (!post) {
    return <ArticleClient params={{ slug }} post={null as any} relatedPosts={[]} renderedContent={null} />
  }

  // Get related posts (same category, excluding current post)
  const allPosts = await getAllPosts()
  const relatedPosts = allPosts.filter((p) => p.category === post.category && p.slug !== post.slug).slice(0, 3)

  // Render MDX content on server side
  const renderedContent = (
    <MDXRemote
      source={post.content}
      components={components}
      options={{
        mdxOptions: {
          rehypePlugins: [rehypeHighlight],
        },
      }}
    />
  )

  const articleUrl = `https://pappahaj.se/artiklar/${slug}`

  return (
    <>
      <ArticleSchema post={post} url={articleUrl} />
      <BreadcrumbSchema
        items={[
          { name: "Hem", url: "https://pappahaj.se" },
          { name: "Artiklar", url: "https://pappahaj.se/artiklar" },
          { name: post.title, url: articleUrl }
        ]}
      />
      <ArticleClient params={{ slug }} post={post} relatedPosts={relatedPosts} renderedContent={renderedContent} />
    </>
  )
}
