import type { Metada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { CalendarD<PERSON>, Clock, ArrowRight, Search, Filter } from "lucide-react"
import { getAllPosts, getAllCategories } from "@/lib/blog"
import { BlogSchema, BreadcrumbSchema } from "@/components/structured-data"

export const metadata: Metadata = {
  title: "Poker Artiklar - Expertstrategier & Analyser | Pappa Haj",
  description:
    "Läs alla expertartiklar från Pappa Haj om pokerstrategi, NL Hold'em, MTT-turneringar, bankroll management, poker psykologi och avancerade tekniker. Över 100+ djupgående analyser och strategiguider för att förbättra ditt pokerspel från nybörjare till proffs.",
  keywords: [
    "poker artiklar sverige",
    "pokerstrategi artiklar",
    "nl holdem artiklar",
    "mtt strategi artiklar",
    "poker tips svenska",
    "bankroll management guide",
    "poker psykologi artiklar",
    "poker coaching artiklar",
    "avancerad pokerstrategi",
    "poker expertråd",
    "poker analyser svenska",
    "pappa haj artiklar"
  ],
  alternates: {
    canonical: "https://pappahaj.se/artiklar",
  },
  openGraph: {
    title: "Poker Artiklar - Expertstrategier & Analyser | Pappa Haj",
    description: "Över 100+ expertartiklar om pokerstrategi, NL Hold'em, MTT-turneringar och avancerade tekniker.",
    url: "https://pappahaj.se/artiklar",
    type: "website",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Pappa Haj - Poker Artiklar & Expertstrategier",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Poker Artiklar - Expertstrategier & Analyser",
    description: "Över 100+ expertartiklar om pokerstrategi och avancerade tekniker.",
    images: ["/images/og-image.jpg"],
  },
}

export default async function ArticlesPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  // Get the category from the URL query parameters
  const categoryParam = searchParams.kategori as string | undefined

  // Get all posts and categories
  const allPosts = await getAllPosts()
  const allCategories = await getAllCategories()

  // Filter posts by category if specified
  const posts = categoryParam ? allPosts.filter((post) => post.category === categoryParam) : allPosts

  return (
    <>
      <BlogSchema />
      <BreadcrumbSchema
        items={[
          { name: "Hem", url: "https://pappahaj.se" },
          { name: "Artiklar", url: "https://pappahaj.se/artiklar" }
        ]}
      />
      <div className="min-h-screen bg-slate-950 py-12 pt-32">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16 relative">
          {/* Background decoration */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-0 left-1/4 text-4xl suit-spade"></div>
            <div className="absolute top-10 right-1/4 text-4xl suit-heart"></div>
          </div>

          <div className="relative">
            <div className="inline-flex items-center px-4 py-2 bg-amber-900/20 border border-amber-500/20 rounded-full mb-6">
              <span className="text-amber-400 text-sm font-medium">Kunskapsbank</span>
            </div>

            <h1 className="font-crimson text-5xl lg:text-6xl font-bold mb-6 text-white">
              Alla{" "}
              <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">
                Artiklar
              </span>
            </h1>

            <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed mb-8">
              Utforska vår omfattande samling av expertartiklar om pokerstrategi, psykologi och tekniker. Från
              nybörjarguider till avancerade koncept - här hittar du allt du behöver för att utveckla ditt spel.
            </p>

            {/* Stats */}
            <div className="flex justify-center items-center gap-8 text-sm text-slate-500">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                <span>{allPosts.length} Artiklar</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                <span>{allCategories.length} Kategorier</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>Uppdateras veckovis</span>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-12">
          <div className="max-w-4xl mx-auto">
            {/* Search Bar */}
            <div className="relative mb-8">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-slate-400" />
              </div>
              <input
                type="text"
                placeholder="Sök artiklar..."
                className="w-full pl-12 pr-4 py-4 bg-slate-800/50 border border-slate-700 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent transition-all"
              />
            </div>

            {/* Category Filter */}
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center gap-2 text-slate-400">
                <Filter className="h-4 w-4" />
                <span className="text-sm font-medium">Filtrera:</span>
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              <Link
                href="/artiklar"
                className={`group relative px-6 py-3 rounded-xl transition-all duration-300 ${
                  !categoryParam
                    ? "bg-gradient-to-r from-amber-500 to-amber-600 text-slate-900 shadow-lg"
                    : "bg-slate-800/50 border border-slate-700 text-slate-300 hover:border-amber-500/50 hover:text-amber-400"
                }`}
              >
                <span className="relative z-10 font-medium">Alla ({allPosts.length})</span>
                {!categoryParam && (
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-amber-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                )}
              </Link>

              {allCategories.map((category) => {
                const categoryCount = allPosts.filter((post) => post.category === category).length
                return (
                  <Link
                    key={category}
                    href={`/artiklar?kategori=${encodeURIComponent(category)}`}
                    className={`group relative px-6 py-3 rounded-xl transition-all duration-300 ${
                      categoryParam === category
                        ? "bg-gradient-to-r from-amber-500 to-amber-600 text-slate-900 shadow-lg"
                        : "bg-slate-800/50 border border-slate-700 text-slate-300 hover:border-amber-500/50 hover:text-amber-400"
                    }`}
                  >
                    <span className="relative z-10 font-medium">
                      {category} ({categoryCount})
                    </span>
                    {categoryParam === category && (
                      <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-amber-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    )}
                  </Link>
                )
              })}
            </div>
          </div>
        </div>

        {/* Results Header */}
        {categoryParam && (
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-white mb-2">Artiklar i kategorin "{categoryParam}"</h2>
                <p className="text-slate-400">
                  {posts.length} {posts.length === 1 ? "artikel" : "artiklar"} hittades
                </p>
              </div>
              <Link href="/artiklar" className="text-amber-400 hover:text-amber-300 text-sm font-medium">
                Rensa filter
              </Link>
            </div>
          </div>
        )}

        {/* Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.length > 0 ? (
            posts.map((post, index) => (
              <article key={post.slug} className="poker-card rounded-xl overflow-hidden group">
                <div className="relative overflow-hidden">
                  <Image
                    src={post.image || "/placeholder.svg?height=300&width=500"}
                    alt={`${post.title} - Pokerstrategi guide inom ${post.category} av expert Pappa Haj`}
                    width={500}
                    height={300}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent"></div>

                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <Link
                      href={`/artiklar?kategori=${encodeURIComponent(post.category)}`}
                      className="px-3 py-1 bg-amber-500 hover:bg-amber-600 text-slate-900 text-xs font-semibold rounded-full transition-colors"
                    >
                      {post.category}
                    </Link>
                  </div>

                  {/* Article Number */}
                  <div className="absolute top-4 right-4">
                    <div className="w-8 h-8 bg-slate-900/80 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-amber-400">#{index + 1}</span>
                    </div>
                  </div>

                  {/* Meta Info */}
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center gap-4 text-xs text-slate-300">
                      <span className="flex items-center gap-1">
                        <CalendarDays className="h-3 w-3" />
                        {new Date(post.date).toLocaleDateString("sv-SE")}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {post.readTime}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="font-crimson text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors line-clamp-2">
                    <Link href={`/artiklar/${post.slug}`}>{post.title}</Link>
                  </h3>
                  <p className="text-slate-400 mb-4 line-clamp-3 leading-relaxed">{post.excerpt}</p>

                  <div className="flex items-center justify-between">
                    <Link
                      href={`/artiklar/${post.slug}`}
                      className="inline-flex items-center text-amber-400 hover:text-amber-300 font-medium group/link"
                    >
                      Läs artikel
                      <ArrowRight className="ml-2 h-4 w-4 group-hover/link:translate-x-1 transition-transform" />
                    </Link>

                    {/* Reading Progress Indicator */}
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-slate-600 rounded-full"></div>
                      <div className="w-1 h-1 bg-slate-600 rounded-full"></div>
                      <div className="w-1 h-1 bg-amber-400 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </article>
            ))
          ) : (
            <div className="col-span-1 md:col-span-2 lg:col-span-3">
              <div className="text-center py-16">
                <div className="bg-slate-800/50 border border-slate-700 rounded-xl p-12 max-w-lg mx-auto">
                  <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Search className="h-8 w-8 text-slate-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-4">Inga artiklar hittades</h3>
                  <p className="text-slate-400 mb-6">
                    {categoryParam
                      ? `Det finns inga artiklar i kategorin "${categoryParam}" ännu.`
                      : "Det finns inga artiklar publicerade ännu."}
                  </p>
                  <Link
                    href="/artiklar"
                    className="inline-flex items-center px-6 py-3 bg-amber-500 hover:bg-amber-600 text-slate-900 rounded-lg font-medium transition-colors"
                  >
                    Visa alla artiklar
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Load More / Pagination could go here */}
        {posts.length > 0 && (
          <div className="text-center mt-16">
            <div className="inline-flex items-center gap-4 text-slate-500 text-sm">
              <span>
                Visar {posts.length} av {allPosts.length} artiklar
              </span>
              {categoryParam && (
                <>
                  <span>•</span>
                  <span>Filtrerat på "{categoryParam}"</span>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  )
}
