# Automatisk Sitemap-generering

Detta system genererar automatiskt en sitemap.xml när nya artiklar läggs till på webbplatsen.

## Hur det fungerar

### 1. Automatisk generering
- Sitemap genereras dynamiskt baserat på alla artiklar i `content/` mappen
- Inkluderar både statiska sidor och dynamiska artikelsidor
- Uppdateras automatiskt när nya artiklar läggs till

### 2. Tillgängliga endpoints

#### Sitemap
- **URL**: `https://pappahaj.se/sitemap.xml`
- **Innehåll**: Alla sidor på webbplatsen med metadata
- **Cache**: 24 timmar

#### Robots.txt
- **URL**: `https://pappahaj.se/robots.txt`
- **Innehåll**: Instruktioner för sökmotorrobotar
- **Hänvisar till**: sitemap.xml

### 3. API endpoints

#### Revalidate (automatisk uppdatering)
```bash
POST /api/revalidate
Headers: x-revalidate-secret: [SECRET]
Body: { "path": "/artiklar/ny-artikel" }
```

#### Manuell sitemap-regenerering
```bash
POST /api/sitemap/regenerate
Headers: x-revalidate-secret: [SECRET]
```

### 4. Inkluderade sidor

#### Statiska sidor
- Hem (`/`) - Prioritet: 1.0
- Artiklar (`/artiklar`) - Prioritet: 0.9
- Om (`/om`) - Prioritet: 0.7
- Kontakt (`/kontakt`) - Prioritet: 0.6
- Integritetspolicy - Prioritet: 0.3
- Cookie-policy - Prioritet: 0.3
- Ansvarsfriskrivning - Prioritet: 0.3

#### Dynamiska sidor
- Alla artiklar (`/artiklar/[slug]`) - Prioritet: 0.8

### 5. Metadata per sida
- **URL**: Fullständig URL till sidan
- **lastModified**: Senast ändrad (artikeldatum för artiklar)
- **changeFrequency**: Hur ofta sidan uppdateras
- **priority**: Sidans prioritet (0.0-1.0)

### 6. Automatisk uppdatering

När du lägger till en ny artikel:
1. Lägg markdown-filen i `content/` mappen
2. Systemet upptäcker automatiskt den nya filen
3. Sitemap uppdateras nästa gång den begärs
4. Använd revalidate API för omedelbar uppdatering

### 7. Miljövariabler

Lägg till i `.env.local`:
```
REVALIDATE_SECRET=din-hemliga-nyckel-här
```

### 8. SEO-fördelar

- Hjälper sökmotorer att hitta alla sidor
- Anger prioritet och uppdateringsfrekvens
- Automatisk uppdatering när innehåll ändras
- Följer XML sitemap-standarden
- Inkluderar både svenska och engelska metadata

### 9. Felsökning

#### Kontrollera sitemap
Besök `https://pappahaj.se/sitemap.xml` för att se aktuell sitemap.

#### Manuell uppdatering
```bash
curl -X POST https://pappahaj.se/api/sitemap/regenerate \
  -H "x-revalidate-secret: DIN_SECRET"
```

#### Validera sitemap
Använd Google Search Console eller online sitemap-validatorer.
