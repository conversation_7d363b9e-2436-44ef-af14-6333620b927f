import { type NextRequest, NextResponse } from "next/server"
import { revalidatePath } from "next/cache"

// This API route is used to manually regenerate the sitemap
export async function POST(request: NextRequest) {
  try {
    // You could add authentication here to ensure only authorized requests can regenerate
    const secret = request.headers.get("x-revalidate-secret")
    if (secret !== process.env.REVALIDATE_SECRET) {
      return NextResponse.json({ message: "Invalid secret" }, { status: 401 })
    }

    // Revalidate the sitemap
    revalidatePath("/sitemap.xml")
    
    // Also revalidate robots.txt in case it needs updating
    revalidatePath("/robots.txt")

    return NextResponse.json({ 
      regenerated: true, 
      message: "Sitemap and robots.txt regenerated successfully",
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({ 
      message: "Error regenerating sitemap", 
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

// GET endpoint to check sitemap status
export async function GET() {
  return NextResponse.json({
    message: "Sitemap regeneration endpoint",
    usage: "POST with x-revalidate-secret header to regenerate sitemap",
    endpoints: {
      sitemap: "/sitemap.xml",
      robots: "/robots.txt"
    }
  })
}
