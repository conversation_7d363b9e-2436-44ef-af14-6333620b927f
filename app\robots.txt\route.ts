export async function GET() {
  const baseUrl = "https://pappahaj.se"
  
  const robotsTxt = `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Disallow admin and API routes
Disallow: /api/
Disallow: /_next/
Disallow: /admin/

# Allow all content for SEO
Allow: /artiklar/
Allow: /blogg/
Allow: /om
Allow: /kontakt
Allow: /integritetspolicy
Allow: /cookie-policy
Allow: /ansvarsfriskrivning

# Crawl delay (optional)
Crawl-delay: 1`

  return new Response(robotsTxt, {
    headers: {
      "Content-Type": "text/plain",
      "Cache-Control": "s-maxage=86400, stale-while-revalidate", // Cache for 24 hours
    },
  })
}
